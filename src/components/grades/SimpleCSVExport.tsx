'use client';

import React, { useState } from 'react';
import { Download, FileText, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { GradeRecord } from '@/types/grades';
import { exportToCSV, downloadFile } from '@/lib/ts/grades/import-export';

interface SimpleCSVExportProps {
	grades: GradeRecord[];
	className?: string;
}

export function SimpleCSVExport({ grades, className }: SimpleCSVExportProps) {
	const [isExporting, setIsExporting] = useState(false);

	const handleExport = async () => {
		if (grades.length === 0) {
			return;
		}

		setIsExporting(true);

		try {
			const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
			const csvContent = exportToCSV(grades, {
				format: 'csv',
				includeCalculated: true,
				includeSemesterStats: false,
				includeOverallStats: false
			});
			const filename = `bang_diem_${timestamp}.csv`;
			downloadFile(csvContent, filename, 'text/csv');
		} catch (error) {
			console.error('Export error:', error);
			alert('Có lỗi xảy ra khi xuất dữ liệu');
		} finally {
			setIsExporting(false);
		}
	};

	return (
		<div className={className}>
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Download className="h-5 w-5" />
						Xuất dữ liệu CSV
					</CardTitle>
					<CardDescription>
						Tải xuống toàn bộ dữ liệu điểm dưới định dạng CSV
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{grades.length === 0 ? (
						<Alert>
							<AlertCircle className="h-4 w-4" />
							<AlertDescription>
								Chưa có dữ liệu để xuất. Vui lòng nhập dữ liệu trước khi xuất file.
							</AlertDescription>
						</Alert>
					) : (
						<div className="space-y-4">
							{/* Export Info */}
							<div className="bg-muted/30 p-4 rounded-lg">
								<div className="flex items-center gap-2 mb-2">
									<FileText className="h-4 w-4 text-muted-foreground" />
									<span className="font-medium">Thông tin xuất file</span>
								</div>
								<div className="text-sm text-muted-foreground space-y-1">
									<p>• Tổng số môn học: {grades.length}</p>
									<p>• Định dạng: CSV (Comma Separated Values)</p>
									<p>• Bao gồm: Tất cả dữ liệu điểm và điểm tính toán</p>
								</div>
							</div>

							{/* Export Button */}
							<Button
								onClick={handleExport}
								disabled={isExporting}
								className="w-full flex items-center gap-2"
								size="lg"
							>
								{isExporting ? (
									<>
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
										Đang xuất file...
									</>
								) : (
									<>
										<Download className="h-4 w-4" />
										Tải xuống file CSV
									</>
								)}
							</Button>

							{/* Format Info */}
							<div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-lg">
								<p className="font-medium mb-2">File CSV sẽ chứa các cột:</p>
								<div className="grid grid-cols-2 gap-1">
									<p>• Tên môn</p>
									<p>• Kỳ</p>
									<p>• Tín chỉ</p>
									<p>• TP1 (Điểm giữa kỳ)</p>
									<p>• TP2 (Điểm chuyên cần)</p>
									<p>• Thi (Điểm thi)</p>
									<p>• ĐQT (Điểm quá trình)</p>
									<p>• KTHP (Kết thúc học phần)</p>
									<p>• KTHP hệ 4</p>
									<p>• Điểm chữ</p>
								</div>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
