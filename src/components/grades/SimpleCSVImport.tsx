'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { parseCSV } from '@/lib/ts/grades/import-export';
import { ImportResult } from '@/types/grades';

interface SimpleCSVImportProps {
	onImportComplete: (result: ImportResult) => void;
	onImportStart?: () => void;
	className?: string;
}

export function SimpleCSVImport({
	onImportComplete,
	onImportStart,
	className
}: SimpleCSVImportProps) {
	const [isProcessing, setIsProcessing] = useState(false);
	const [importResult, setImportResult] = useState<ImportResult | null>(null);
	const [dragActive, setDragActive] = useState(false);

	const processFile = useCallback(
		async (file: File) => {
			setIsProcessing(true);
			setImportResult(null);
			onImportStart?.();

			try {
				const content = await file.text();
				let result: ImportResult;

				if (file.name.toLowerCase().endsWith('.csv')) {
					result = await parseCSV(content);
					// Filter out invalid records - only keep valid ones
					const validData = result.data.filter((grade) => grade.isValid);
					result = {
						...result,
						data: validData,
						success: validData.length > 0,
						validRecords: validData.length,
						invalidRecords: 0, // We don't include invalid records
						errors: [], // Clear errors since we're not including invalid data
						warnings: []
					};
				} else {
					result = {
						success: false,
						data: [],
						errors: ['Chỉ hỗ trợ file CSV. Vui lòng chọn file .csv'],
						warnings: [],
						totalRecords: 0,
						validRecords: 0,
						invalidRecords: 0
					};
				}

				setImportResult(result);
				onImportComplete(result);
			} catch (error) {
				const errorResult: ImportResult = {
					success: false,
					data: [],
					errors: [
						`Lỗi đọc file: ${error instanceof Error ? error.message : 'Lỗi không xác định'}`
					],
					warnings: [],
					totalRecords: 0,
					validRecords: 0,
					invalidRecords: 0
				};
				setImportResult(errorResult);
				onImportComplete(errorResult);
			} finally {
				setIsProcessing(false);
			}
		},
		[onImportComplete, onImportStart]
	);

	const onDrop = useCallback(
		(acceptedFiles: File[]) => {
			if (acceptedFiles.length > 0 && acceptedFiles[0]) {
				processFile(acceptedFiles[0]);
			}
		},
		[processFile]
	);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: {
			'text/csv': ['.csv']
		},
		multiple: false,
		disabled: isProcessing,
		onDragEnter: () => setDragActive(true),
		onDragLeave: () => setDragActive(false)
	});

	const clearResult = () => {
		setImportResult(null);
	};

	return (
		<div className={className}>
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Upload className="h-5 w-5" />
						Nhập dữ liệu từ CSV
					</CardTitle>
					<CardDescription>
						Tải lên file CSV chứa dữ liệu điểm. Chỉ dữ liệu hợp lệ sẽ được nhập vào hệ thống.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* File Drop Zone */}
					<div
						{...getRootProps()}
						className={`
							border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
							${
								isDragActive || dragActive
									? 'border-primary bg-primary/5'
									: 'border-muted-foreground/25 hover:border-primary/50'
							}
							${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}
						`}
					>
						<input {...getInputProps()} />
						<div className="flex flex-col items-center gap-4">
							<div className="p-4 rounded-full bg-muted">
								<FileText className="h-8 w-8 text-muted-foreground" />
							</div>
							<div>
								<p className="text-lg font-medium">
									{isDragActive || dragActive
										? 'Thả file CSV vào đây...'
										: 'Kéo thả file CSV hoặc click để chọn'}
								</p>
								<p className="text-sm text-muted-foreground mt-1">
									Chỉ hỗ trợ file .csv (tối đa 10MB)
								</p>
							</div>
							{isProcessing && (
								<div className="flex items-center gap-2 text-sm text-muted-foreground">
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
									Đang xử lý file...
								</div>
							)}
						</div>
					</div>

					{/* Import Result */}
					{importResult && (
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<h4 className="font-medium">Kết quả nhập dữ liệu</h4>
								<Button variant="ghost" size="sm" onClick={clearResult}>
									<X className="h-4 w-4" />
								</Button>
							</div>

							{importResult.success ? (
								<Alert className="border-green-200 bg-green-50 dark:bg-green-950">
									<CheckCircle className="h-4 w-4 text-green-600" />
									<AlertDescription>
										<div className="space-y-1">
											<p className="font-medium text-green-800 dark:text-green-200">
												Nhập dữ liệu thành công!
											</p>
											<p className="text-sm text-green-700 dark:text-green-300">
												Đã nhập {importResult.validRecords} bản ghi hợp lệ.
											</p>
										</div>
									</AlertDescription>
								</Alert>
							) : (
								<Alert className="border-red-200 bg-red-50 dark:bg-red-950">
									<AlertCircle className="h-4 w-4 text-red-600" />
									<AlertDescription>
										<div className="space-y-2">
											<p className="font-medium text-red-800 dark:text-red-200">
												Nhập dữ liệu thất bại
											</p>
											{importResult.errors.length > 0 && (
												<div className="text-sm text-red-700 dark:text-red-300">
													<p className="font-medium">Lỗi:</p>
													<ul className="list-disc list-inside space-y-1">
														{importResult.errors.slice(0, 5).map((error, index) => (
															<li key={index}>{error}</li>
														))}
														{importResult.errors.length > 5 && (
															<li>... và {importResult.errors.length - 5} lỗi khác</li>
														)}
													</ul>
												</div>
											)}
										</div>
									</AlertDescription>
								</Alert>
							)}
						</div>
					)}

					{/* Format Info */}
					<div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-lg">
						<p className="font-medium mb-2">Định dạng file CSV yêu cầu:</p>
						<p>Các cột: Tên môn, Kỳ, Tín, TP1, TP2, Thi</p>
						<p className="mt-1">
							<strong>Lưu ý:</strong> Chỉ dữ liệu hợp lệ sẽ được nhập. Dữ liệu lỗi sẽ bị bỏ qua.
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
